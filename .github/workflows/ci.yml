name: CI

on:
  push:
    branches: [ main, master ]
  pull_request:

permissions:
  contents: read

concurrency:
  group: ci-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build-and-test:
    runs-on: ubuntu-latest
    env:
      # Required environment variables for build process
      DATABASE_URL: postgresql://ci_user:ci_password@localhost:5432/ci_db
      REDIS_URL: redis://localhost:6379
      NEXT_PUBLIC_APP_URL: https://ci-build.example.com
      SESSION_SECRET: ci_session_secret_for_build_only
      NODE_ENV: production
      USE_LOCAL_AUTH: true
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'npm'
          cache-dependency-path: package-lock.json
      - name: Clean npm cache
        run: npm cache clean --force
      - name: Install dependencies
        run: npm ci
      - name: Verify TypeScript installation
        run: ./node_modules/.bin/tsc --version
      - name: Run linting
        run: npm run lint
      - name: Build application
        run: npm run build --if-present
      - name: Run tests
        run: npm test -- --run

